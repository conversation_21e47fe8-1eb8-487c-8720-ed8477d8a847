-- Current teacher fields in the users table
CREATE TABLE IF NOT EXISTS users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  name VA<PERSON><PERSON><PERSON>(100), -- sometimes referred to as full_name
  full_name <PERSON><PERSON><PERSON><PERSON>(100), -- in some queries
  email VARCHAR(100) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  role ENUM('admin', 'teacher', 'student', 'parent') NOT NULL,
  profile_image VARCHAR(255),
  subjects TEXT, -- comma-separated list of subjects
  bio TEXT,
  date_of_birth DATE,
  gender ENUM('male', 'female', 'other'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login TIMESTAMP NULL,
  is_active BOOLEAN DEFAULT TRUE
);
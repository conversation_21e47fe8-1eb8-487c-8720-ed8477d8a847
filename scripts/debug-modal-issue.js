/**
 * Debug Modal Black Screen Issue
 */

const axios = require('axios');

async function debugModalIssue() {
  try {
    console.log('🔍 Debugging Modal Black Screen Issue...\n');

    const baseURL = 'http://localhost:3018';

    // Create a session by doing demo login first
    console.log('1. Performing demo login...');
    
    const loginResponse = await axios.post(`${baseURL}/demo-login`, {
      role: 'principal'
    }, {
      maxRedirects: 0,
      validateStatus: function (status) {
        return status >= 200 && status < 400;
      }
    });

    // Extract cookies from login response
    const cookies = loginResponse.headers['set-cookie'];
    console.log('✅ Demo login successful, got session cookies');

    // Test the classroom API endpoint
    console.log('\n2. Testing classroom API endpoint...');
    
    try {
      const apiResponse = await axios.get(`${baseURL}/principal/api/classroom/7`, {
        headers: {
          'Cookie': cookies ? cookies.join('; ') : ''
        }
      });

      console.log('✅ API Response Status:', apiResponse.status);
      console.log('✅ API Response Data:', JSON.stringify(apiResponse.data, null, 2));
      
      if (apiResponse.data.success) {
        console.log('✅ API is working correctly');
        console.log('   - Classroom data:', apiResponse.data.data.classroom);
        console.log('   - Students count:', apiResponse.data.data.students.total);
        console.log('   - Equipment count:', {
          it: apiResponse.data.data.equipment.it.length,
          electrical: apiResponse.data.data.equipment.electrical.length
        });
      } else {
        console.log('❌ API returned error:', apiResponse.data.error);
      }
      
    } catch (apiError) {
      console.log('❌ API Error:', apiError.response?.status, apiError.response?.data || apiError.message);
    }

    // Test infrastructure page
    console.log('\n3. Testing infrastructure page...');
    
    const infraResponse = await axios.get(`${baseURL}/principal/infrastructure`, {
      headers: {
        'Cookie': cookies ? cookies.join('; ') : ''
      }
    });

    if (infraResponse.status === 200) {
      console.log('✅ Infrastructure page accessible');
      
      const content = infraResponse.data;
      
      // Check for classroom cards with data-room attributes
      const dataRoomMatches = content.match(/data-room="(\d+)"/g);
      if (dataRoomMatches) {
        console.log('✅ Found classroom cards with data-room attributes:', dataRoomMatches.length);
        console.log('   Room IDs:', dataRoomMatches.map(match => match.match(/\d+/)[0]));
      } else {
        console.log('❌ No classroom cards with data-room attributes found');
      }
      
      // Check if modal structure exists
      if (content.includes('id="classroomModal"')) {
        console.log('✅ Modal structure found');
      } else {
        console.log('❌ Modal structure NOT found');
      }
      
      // Check if infrastructure.js is included
      if (content.includes('src="/js/infrastructure.js"')) {
        console.log('✅ infrastructure.js script included');
      } else {
        console.log('❌ infrastructure.js script NOT included');
      }
      
    } else {
      console.log('❌ Infrastructure page not accessible, status:', infraResponse.status);
    }

    console.log('\n=== Debug Complete ===');
    console.log('\n🔍 Possible Issues:');
    console.log('1. Check browser console for JavaScript errors');
    console.log('2. Verify API authentication is working');
    console.log('3. Check if classroom data exists in database');
    console.log('4. Verify modal content generation is working');
    console.log('\n🎯 Next Steps:');
    console.log('1. Open browser dev tools');
    console.log('2. Go to Infrastructure page');
    console.log('3. Click on a classroom card');
    console.log('4. Check Console and Network tabs for errors');

  } catch (error) {
    if (error.response && error.response.status === 302) {
      console.log('✅ Got redirect (expected for demo login)');
      console.log('   Redirect to:', error.response.headers.location);
    } else {
      console.error('Error debugging modal issue:', error.message);
    }
  }
}

// Run the debug
debugModalIssue();

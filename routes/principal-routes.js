/**
 * Principal Routes
 * Handles all routes for the Principal dashboard and functionality
 */

const express = require('express');
const router = express.Router();
const principalController = require('../controllers/principal-controller');
const { checkAuthenticated } = require('../middleware/auth');

// Middleware to check if user is principal or admin
const checkPrincipal = (req, res, next) => {
  if (req.session.userRole === 'admin' || req.session.userRole === 'principal') {
    return next();
  }

  res.status(403).render('error', {
    title: 'Access Denied',
    message: 'You do not have permission to access the Principal dashboard. This feature requires principal or admin privileges.',
    error: { status: 403 },
    layout: 'layouts/main'
  });
};

// Apply authentication middleware to all routes
router.use(checkAuthenticated);
router.use(checkPrincipal);

// Middleware to ensure principal layout is used
router.use((req, res, next) => {
  res.locals.layout = 'layouts/principal';
  next();
});

// Dashboard
router.get('/dashboard', principalController.getDashboard);
router.get('/', principalController.getDashboard);

// Academic Progress
router.get('/academic-progress', principalController.getAcademicProgress);

// Teacher Management
router.get('/teacher-management', principalController.getTeacherManagement);
router.get('/teacher-timetables', principalController.getTeacherTimetables);
router.get('/teacher/:id', principalController.getTeacherDetails);

// Enhanced teacher profile API (accessible to principal)
const teacherProfileEnhancedApi = require('./api/teacher-profile-enhanced-api');
router.use('/api/teacher', teacherProfileEnhancedApi);

// Student Analytics
router.get('/student-analytics', principalController.getStudentAnalytics);

// Infrastructure Overview
router.get('/infrastructure', principalController.getInfrastructure);

// Profile
router.get('/profile', principalController.getProfile);

// Reports and Analytics
router.get('/reports', principalController.getReports);
router.get('/reports/academic', principalController.getAcademicReports);
router.get('/reports/attendance', principalController.getAttendanceReports);
router.get('/reports/performance', principalController.getPerformanceReports);

// API endpoints for real-time data
router.get('/api/dashboard-stats', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get real-time statistics
    const [stats] = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students,
        (SELECT COUNT(*) FROM classes) as total_classes,
        (SELECT COUNT(*) FROM subjects) as total_subjects
    `);

    // Get today's lecture statistics
    const [lectureStats] = await db.query(`
      SELECT
        COUNT(*) as total_lectures_today,
        SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_lectures
      FROM teacher_lectures
      WHERE date = CURDATE()
    `);

    // Get syllabus progress
    const [syllabusProgress] = await db.query(`
      SELECT
        AVG(CASE WHEN status = 'completed' THEN 100 ELSE 0 END) as avg_completion
      FROM teacher_lectures
    `);

    res.json({
      success: true,
      data: {
        ...stats[0],
        ...lectureStats[0],
        syllabus_completion: syllabusProgress[0]?.avg_completion || 0
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch statistics' });
  }
});

// API endpoint for academic progress data
router.get('/api/academic-progress', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get class-wise syllabus completion
    const [classProgress] = await db.query(`
      SELECT
        c.name as class_name,
        s.name as subject_name,
        COUNT(tl.id) as total_topics,
        SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) as completed_topics,
        ROUND((SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_percentage
      FROM classes c
      LEFT JOIN teacher_lectures tl ON c.name = tl.class_name
      LEFT JOIN subjects s ON s.name = tl.subject_name
      GROUP BY c.name, s.name
      ORDER BY c.name, s.name
    `);

    res.json({
      success: true,
      data: classProgress
    });
  } catch (error) {
    console.error('Error fetching academic progress:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch academic progress' });
  }
});

// API endpoint for teacher performance data
router.get('/api/teacher-performance', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get teacher performance metrics
    const [teacherPerformance] = await db.query(`
      SELECT
        u.name as teacher_name,
        u.email as teacher_email,
        COUNT(tl.id) as total_lectures,
        SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        SUM(CASE WHEN tl.status = 'pending' AND tl.date < CURDATE() THEN 1 ELSE 0 END) as overdue_lectures,
        ROUND((SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_rate
      FROM users u
      LEFT JOIN teacher_lectures tl ON u.id = tl.teacher_id
      WHERE u.role = 'teacher' AND u.is_active = 1
      GROUP BY u.id, u.name, u.email
      ORDER BY completion_rate DESC
    `);

    res.json({
      success: true,
      data: teacherPerformance
    });
  } catch (error) {
    console.error('Error fetching teacher performance:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch teacher performance' });
  }
});

// API endpoint for classroom details
router.get('/api/classroom/:roomId', async (req, res) => {
  try {
    const { roomId } = req.params;
    const db = require('../config/database');

    console.log('🔍 API: Fetching classroom details for roomId:', roomId);

    // Get detailed classroom information from rooms table
    // Try to find by ID first, then by room_number
    const [roomInfo] = await db.query(`
      SELECT
        id,
        room_number,
        floor,
        capacity,
        building
      FROM rooms
      WHERE id = ? OR room_number = ? OR room_number = ?
    `, [roomId, roomId, `Room ${roomId}`]);

    console.log('🔍 API: Room query result:', roomInfo);

    if (roomInfo.length === 0) {
      console.log('❌ API: Room not found for roomId:', roomId);
      return res.status(404).json({ success: false, error: 'Room not found' });
    }

    const classroom = roomInfo[0];

    // Get students assigned to this room through student_classrooms relationship
    // First try the students table, if it doesn't exist, use users table
    let students = [];
    try {
      const [studentsResult] = await db.query(`
        SELECT
          id,
          student_id,
          name,
          father_name,
          mother_name,
          gender,
          class,
          section,
          session,
          roll_no,
          contact_no,
          admission_no,
          dob,
          height,
          weight,
          caste_category_name,
          religion_name,
          medium_name,
          bpl,
          disability,
          cur_address,
          village_ward,
          pin_code
        FROM students
        WHERE (room_number = ? OR room_number = ?) AND is_active = 1
        ORDER BY class, section, roll_no, name
      `, [classroom.room_number, `Room ${roomId}`]);
      students = studentsResult;
      console.log('🔍 API: Students query result from students table:', students.length, 'students found');
    } catch (error) {
      console.log('🔍 API: Students table not found, trying student_classrooms approach...');

      // Try to get students through student_classrooms and classrooms relationship
      try {
        const [studentsResult] = await db.query(`
          SELECT DISTINCT
            u.id,
            u.username as student_id,
            u.name,
            u.full_name as father_name,
            '' as mother_name,
            'Male' as gender,
            cl.grade as class,
            cl.section,
            c.session,
            '' as roll_no,
            '' as contact_no,
            '' as admission_no,
            u.date_of_birth as dob,
            0 as height,
            0 as weight,
            '' as caste_category_name,
            '' as religion_name,
            '' as medium_name,
            'No' as bpl,
            'No' as disability,
            '' as cur_address,
            '' as village_ward,
            '' as pin_code
          FROM student_classrooms sc
          JOIN users u ON sc.student_id = u.id
          JOIN classrooms c ON sc.classroom_id = c.id
          JOIN classes cl ON c.class_id = cl.id
          JOIN rooms r ON c.room_id = r.id
          WHERE r.id = ? AND u.role = 'student' AND sc.status = 'active'
          ORDER BY cl.grade, cl.section, u.name
        `, [classroom.id]);
        students = studentsResult;
        console.log('🔍 API: Students query result from student_classrooms:', students.length, 'students found');
      } catch (error2) {
        console.log('🔍 API: No student_classrooms table either, trying users table directly...');

        // Final fallback: try to get students from users table directly
        try {
          const [studentsResult] = await db.query(`
            SELECT DISTINCT
              u.id,
              u.username as student_id,
              u.name,
              u.full_name as father_name,
              '' as mother_name,
              'Unknown' as gender,
              '' as class,
              '' as section,
              '' as session,
              '' as roll_no,
              '' as contact_no,
              '' as admission_no,
              u.date_of_birth as dob,
              0 as height,
              0 as weight,
              '' as caste_category_name,
              '' as religion_name,
              '' as medium_name,
              'No' as bpl,
              'No' as disability,
              '' as cur_address,
              '' as village_ward,
              '' as pin_code
            FROM users u
            WHERE u.role = 'student'
            ORDER BY u.name
            LIMIT 5
          `);
          students = studentsResult;
          console.log('🔍 API: Students query result from users table (sample):', students.length, 'students found');
        } catch (error3) {
          console.log('🔍 API: No student data available, using empty array');
          students = [];
        }
      }
    }

    // Get IT equipment for this room using foreign key relationship from inventory_items
    const [itEquipment] = await db.query(`
      SELECT
        i.name as item_name,
        CASE
          WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 'projector'
          WHEN i.name LIKE '%UPS%' THEN 'other'
          WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 'desktop'
          WHEN i.name LIKE '%Laptop%' THEN 'laptop'
          WHEN i.name LIKE '%PANEL%' THEN 'other'
          WHEN i.name LIKE '%CAMERA%' THEN 'other'
          WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 'printer'
          WHEN i.name LIKE '%Router%' THEN 'network'
          ELSE 'other'
        END as item_type,
        i.serial_number,
        i.status,
        i.manufacturer,
        i.model,
        i.purchase_date,
        i.warranty_expiry,
        i.notes,
        'good' as condition_status,
        NULL as mac_address,
        NULL as ip_address,
        NULL as hostname,
        i.description,
        i.purchase_cost
      FROM inventory_items i
      WHERE i.room_id = ?
      ORDER BY
        CASE
          WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 1
          WHEN i.name LIKE '%UPS%' THEN 2
          WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 3
          WHEN i.name LIKE '%Laptop%' THEN 4
          WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 5
          ELSE 6
        END, i.name
    `, [classroom.id]);

    // Get electrical equipment for this room with detailed breakdown
    const [electricalEquipment] = await db.query(`
      SELECT
        item_name,
        item_type,
        serial_number,
        status,
        manufacturer,
        model,
        wattage,
        installation_date,
        notes
      FROM electrical_inventory
      WHERE room_number = ? OR room_number = ?
      ORDER BY item_type, item_name
    `, [classroom.room_number, `Room ${roomId}`]);

    console.log('🔍 API: Equipment query results:', {
      it: itEquipment.length,
      electrical: electricalEquipment.length
    });

    // Calculate gender breakdown from the new students table
    const boys = students.filter(s => s.gender === 'Male');
    const girls = students.filter(s => s.gender === 'Female');
    const others = students.filter(s => s.gender === 'Other');

    // Get class distribution
    const classDistribution = students.reduce((acc, student) => {
      const classKey = `${student.class}-${student.section || 'N/A'}`;
      if (!acc[classKey]) {
        acc[classKey] = { class: student.class, section: student.section, count: 0, students: [] };
      }
      acc[classKey].count++;
      acc[classKey].students.push(student);
      return acc;
    }, {});

    // Determine classroom assignment based on students
    let classroomName = 'Unassigned';
    if (students.length > 0) {
      const mostCommonClass = Object.values(classDistribution)
        .sort((a, b) => b.count - a.count)[0];
      if (mostCommonClass) {
        classroomName = `Class ${mostCommonClass.class}${mostCommonClass.section ? ` - ${mostCommonClass.section}` : ''}`;
      }
    }

    const responseData = {
      success: true,
      data: {
        classroom: {
          room_number: classroom.room_number,
          floor: classroom.floor,
          capacity: classroom.capacity,
          building: classroom.building,
          full_name: classroomName,
          incharge_name: null // Will be updated when teacher assignment is implemented
        },
        students: {
          total: students.length,
          boys: boys.length,
          girls: girls.length,
          others: others.length,
          list: students,
          boys_list: boys,
          girls_list: girls,
          others_list: others,
          class_distribution: Object.values(classDistribution)
        },
        equipment: {
          it: itEquipment,
          electrical: electricalEquipment,
          summary: {
            total_it: itEquipment.length,
            total_electrical: electricalEquipment.length,
            working_it: itEquipment.filter(item => item.status === 'working').length,
            working_electrical: electricalEquipment.filter(item => item.status === 'working').length,
            faulty_it: itEquipment.filter(item => item.status === 'faulty').length,
            faulty_electrical: electricalEquipment.filter(item => item.status === 'faulty').length
          }
        }
      }
    };

    console.log('✅ API: Sending response for room', roomId, ':', {
      room_number: classroom.room_number,
      students: students.length,
      it_equipment: itEquipment.length,
      electrical_equipment: electricalEquipment.length
    });

    res.json(responseData);
  } catch (error) {
    console.error('Error fetching classroom details:', error);

    // Return a more user-friendly response with basic room info
    res.json({
      success: true,
      data: {
        classroom: {
          room_number: `Room ${req.params.roomId}`,
          floor: 1,
          capacity: 50,
          building: null,
          full_name: 'Classroom Information',
          incharge_name: null
        },
        students: {
          total: 0,
          boys: 0,
          girls: 0,
          others: 0,
          list: [],
          boys_list: [],
          girls_list: [],
          others_list: [],
          class_distribution: []
        },
        equipment: {
          it: [],
          electrical: [],
          summary: {
            total_it: 0,
            total_electrical: 0,
            working_it: 0,
            working_electrical: 0,
            faulty_it: 0,
            faulty_electrical: 0
          }
        }
      },
      message: 'Classroom data partially available. Some features may be limited due to database configuration.'
    });
  }
});

// Student Data Management (Read-only for Principal)
router.get('/students', async (req, res) => {
    try {
        const db = require('../config/database');

        // Get pagination parameters
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 25;
        const offset = (page - 1) * limit;

        // Get filter parameters
        const filters = {
            class: req.query.class || '',
            section: req.query.section || '',
            session: req.query.session || '',
            gender: req.query.gender || '',
            stream: req.query.stream || '',
            bpl: req.query.bpl || '',
            disability: req.query.disability || '',
            search: req.query.search || ''
        };

        // Build WHERE clause
        let whereConditions = ['is_active = 1'];
        let queryParams = [];

        if (filters.class) {
            whereConditions.push('class = ?');
            queryParams.push(filters.class);
        }

        if (filters.section) {
            whereConditions.push('section = ?');
            queryParams.push(filters.section);
        }

        if (filters.session) {
            whereConditions.push('session = ?');
            queryParams.push(filters.session);
        }

        if (filters.gender) {
            whereConditions.push('gender = ?');
            queryParams.push(filters.gender);
        }

        if (filters.stream) {
            whereConditions.push('stream = ?');
            queryParams.push(filters.stream);
        }

        if (filters.bpl) {
            whereConditions.push('bpl = ?');
            queryParams.push(filters.bpl);
        }

        if (filters.disability) {
            whereConditions.push('disability = ?');
            queryParams.push(filters.disability);
        }

        if (filters.search) {
            whereConditions.push('(name LIKE ? OR student_id LIKE ? OR father_name LIKE ? OR roll_no LIKE ? OR admission_no LIKE ?)');
            const searchPattern = `%${filters.search}%`;
            queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get total count
        const [countResult] = await db.query(
            `SELECT COUNT(*) as total FROM students WHERE ${whereClause}`,
            queryParams
        );
        const totalStudents = countResult[0].total;
        const totalPages = Math.ceil(totalStudents / limit);

        // Get students with pagination
        const [students] = await db.query(
            `SELECT * FROM students
             WHERE ${whereClause}
             ORDER BY class, section, roll_no, name
             LIMIT ? OFFSET ?`,
            [...queryParams, limit, offset]
        );

        // Check if export is requested
        if (req.query.export === 'excel') {
            const XLSX = require('xlsx');

            // Prepare data for export
            const exportData = students.map(student => ({
                'S.No': student.sno || '',
                'Student ID': student.student_id || '',
                'UdiseCode': student.udise_code || '',
                'Name': student.name || '',
                'Father Name': student.father_name || '',
                'Mother Name': student.mother_name || '',
                'DOB': student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '',
                'Gender': student.gender || '',
                'Class': student.class || '',
                'Section': student.section || '',
                'Stream': student.stream || '',
                'Trade': student.trade || '',
                'Caste Category': student.caste_category_name || '',
                'BPL': student.bpl || '',
                'Disability': student.disability || '',
                'Religion': student.religion_name || '',
                'Medium': student.medium_name || '',
                'Height': student.height || '',
                'Weight': student.weight || '',
                'Admission No': student.admission_no || '',
                'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '',
                'State': student.state_name || '',
                'District': student.district_name || '',
                'Address': student.cur_address || '',
                'Village/Ward': student.village_ward || '',
                'Gram Panchayat': student.gram_panchayat || '',
                'Pin Code': student.pin_code || '',
                'Roll No': student.roll_no || '',
                'Contact No': student.contact_no || '',
                'IFSC Code': student.ifsc_code || '',
                'Bank Name': student.bank_name || '',
                'Column1': student.column1 || '',
                'Account Holder Code': student.account_holder_code || '',
                'Account Holder': student.account_holder || '',
                'Account Holder Name': student.account_holder_name || ''
            }));

            // Create workbook and worksheet
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(exportData);

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Students');

            // Generate buffer
            const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

            // Set headers for download
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="students_export_${new Date().toISOString().split('T')[0]}.xlsx"`);

            return res.send(buffer);
        }

        // Get filter options
        const [classes] = await db.query('SELECT DISTINCT class FROM students WHERE class IS NOT NULL AND class != "" ORDER BY class');
        const [sections] = await db.query('SELECT DISTINCT section FROM students WHERE section IS NOT NULL AND section != "" ORDER BY section');
        const [sessions] = await db.query('SELECT DISTINCT session FROM students WHERE session IS NOT NULL AND session != "" ORDER BY session');
        const [streams] = await db.query('SELECT DISTINCT stream FROM students WHERE stream IS NOT NULL AND stream != "" ORDER BY stream');

        res.render('principal/students', {
            title: 'Student Data Overview',
            pageTitle: 'Student Data Overview',
            layout: 'layouts/principal',
            currentPage: 'students',
            students,
            classes: classes.map(c => c.class),
            sections: sections.map(s => s.section),
            sessions: sessions.map(s => s.session),
            streams: streams.map(s => s.stream),
            pagination: {
                currentPage: page,
                totalPages,
                totalStudents,
                limit,
                hasNext: page < totalPages,
                hasPrev: page > 1
            },
            filters,
            flashSuccess: req.session.flashSuccess,
            flashError: req.session.flashError,
            flashInfo: req.session.flashInfo
        });

        // Clear flash messages
        delete req.session.flashSuccess;
        delete req.session.flashError;
        delete req.session.flashInfo;

    } catch (error) {
        console.error('Error loading student data page:', error);
        req.session.flashError = 'Error loading student data';
        res.redirect('/principal/dashboard');
    }
});

// Get trash data for modal (must come before /:id route)
router.get('/students/trash-data', async (req, res) => {
    try {
        const db = require('../config/database');

        // Get soft deleted students
        const [students] = await db.query(
            `SELECT
                id, student_id, name, class, section, contact_no, updated_at
             FROM students
             WHERE is_active = 0
             ORDER BY updated_at DESC
             LIMIT 50`
        );

        res.json({
            success: true,
            students: students
        });

    } catch (error) {
        console.error('Error fetching trash data:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching trash data'
        });
    }
});

// Get single student data for modal
router.get('/students/:id', async (req, res) => {
    try {
        const db = require('../config/database');
        const studentId = req.params.id;

        const [students] = await db.query(
            'SELECT * FROM students WHERE id = ? AND is_active = 1',
            [studentId]
        );

        if (students.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Student not found'
            });
        }

        res.json({
            success: true,
            student: students[0]
        });

    } catch (error) {
        console.error('Error fetching student:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching student data'
        });
    }
});

// Update single student (read-only for principal, but keeping for consistency)
router.put('/students/:id', async (req, res) => {
    try {
        const db = require('../config/database');
        const studentId = req.params.id;
        const updateData = req.body;

        // Remove empty values
        Object.keys(updateData).forEach(key => {
            if (updateData[key] === '' || updateData[key] === null) {
                delete updateData[key];
            }
        });

        if (Object.keys(updateData).length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No data to update'
            });
        }

        const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
        const updateValues = Object.values(updateData);
        updateValues.push(studentId);

        await db.query(
            `UPDATE students SET ${updateFields}, updated_at = NOW() WHERE id = ? AND is_active = 1`,
            updateValues
        );

        res.json({
            success: true,
            message: 'Student updated successfully'
        });

    } catch (error) {
        console.error('Error updating student:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating student'
        });
    }
});

// Export students data route
router.get('/students/export', async (req, res) => {
    try {
        const db = require('../config/database');

        // Get filter parameters (same as students route)
        const filters = {
            class: req.query.class || '',
            section: req.query.section || '',
            session: req.query.session || '',
            gender: req.query.gender || '',
            stream: req.query.stream || '',
            bpl: req.query.bpl || '',
            disability: req.query.disability || '',
            search: req.query.search || ''
        };

        // Build WHERE clause
        let whereConditions = ['s.is_active = 1'];
        let queryParams = [];

        if (filters.class) {
            whereConditions.push('s.class = ?');
            queryParams.push(filters.class);
        }
        if (filters.section) {
            whereConditions.push('s.section = ?');
            queryParams.push(filters.section);
        }
        if (filters.session) {
            whereConditions.push('s.session = ?');
            queryParams.push(filters.session);
        }
        if (filters.gender) {
            whereConditions.push('s.gender = ?');
            queryParams.push(filters.gender);
        }
        if (filters.stream) {
            whereConditions.push('s.stream = ?');
            queryParams.push(filters.stream);
        }
        if (filters.bpl) {
            whereConditions.push('s.bpl = ?');
            queryParams.push(filters.bpl);
        }
        if (filters.disability) {
            whereConditions.push('s.disability = ?');
            queryParams.push(filters.disability);
        }
        if (filters.search) {
            whereConditions.push('(s.name LIKE ? OR s.student_id LIKE ? OR s.father_name LIKE ? OR s.roll_no LIKE ?)');
            const searchTerm = `%${filters.search}%`;
            queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get all students matching filters (no pagination for export)
        const [students] = await db.query(
            `SELECT
                s.id, s.student_id, s.udise_code, s.name, s.father_name, s.mother_name,
                s.dob, s.gender, s.class, s.section, s.stream, s.trade, s.caste_category_name,
                s.bpl, s.disability, s.religion_name, s.medium_name, s.height, s.weight,
                s.admission_no, s.admission_date, s.state_name, s.district_name, s.cur_address,
                s.village_ward, s.gram_panchayat, s.pin_code, s.roll_no, s.contact_no,
                s.ifsc_code, s.bank_name, s.account_holder, s.account_holder_name,
                s.account_holder_code, s.session, s.created_at, s.updated_at
             FROM students s
             WHERE ${whereClause}
             ORDER BY s.class, s.section, s.roll_no, s.name`,
            queryParams
        );

        // Prepare CSV content
        const headers = [
            'S.No', 'Student ID', 'UdiseCode', 'Name', 'Father Name', 'Mother Name', 'DOB', 'Gender',
            'Class', 'Section', 'Stream', 'Trade', 'Caste Category', 'BPL', 'Disability',
            'Religion', 'Medium', 'Height', 'Weight', 'Admission No', 'Admission Date',
            'State', 'District', 'Address', 'Village/Ward', 'Gram Panchayat', 'Pin Code',
            'Roll No', 'Contact No', 'IFSC Code', 'Bank Name', 'Account Holder',
            'Account Holder Name', 'Account Holder Code', 'Session'
        ];

        let csvContent = headers.join(',') + '\n';

        students.forEach((student, index) => {
            const row = [
                index + 1,
                student.student_id || '',
                student.udise_code || '',
                student.name || '',
                student.father_name || '',
                student.mother_name || '',
                student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '',
                student.gender || '',
                student.class || '',
                student.section || '',
                student.stream || '',
                student.trade || '',
                student.caste_category_name || '',
                student.bpl || '',
                student.disability || '',
                student.religion_name || '',
                student.medium_name || '',
                student.height || '',
                student.weight || '',
                student.admission_no || '',
                student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '',
                student.state_name || '',
                student.district_name || '',
                student.cur_address || '',
                student.village_ward || '',
                student.gram_panchayat || '',
                student.pin_code || '',
                student.roll_no || '',
                student.contact_no || '',
                student.ifsc_code || '',
                student.bank_name || '',
                student.account_holder || '',
                student.account_holder_name || '',
                student.account_holder_code || '',
                student.session || ''
            ];

            // Escape commas and quotes in data
            const escapedRow = row.map(field => {
                const fieldStr = String(field);
                if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
                    return '"' + fieldStr.replace(/"/g, '""') + '"';
                }
                return fieldStr;
            });

            csvContent += escapedRow.join(',') + '\n';
        });

        // Set headers for CSV download
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `students_export_${timestamp}.csv`;

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.send(csvContent);

    } catch (error) {
        console.error('Error exporting students:', error);
        res.status(500).json({
            success: false,
            message: 'Error exporting student data'
        });
    }
});

module.exports = router;

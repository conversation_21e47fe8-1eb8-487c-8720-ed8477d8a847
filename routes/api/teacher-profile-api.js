/**
 * Teacher Profile API Routes
 */

const express = require('express');
const router = express.Router();
const db = require('../../config/database');
const { checkAuthenticated } = require('../../middleware/auth');

// Middleware to check if user is a teacher or admin
const checkTeacher = (req, res, next) => {
  console.log('Session data:', req.session);
  if (req.session.userRole === 'teacher' || req.session.userRole === 'admin') {
    return next();
  }
  res.status(403).json({
    success: false,
    message: 'Access denied. Teacher or admin role required.'
  });
};

// Apply middleware to all routes
router.use(checkAuthenticated);
router.use(checkTeacher);

/**
 * Get teacher profile data including subjects and assigned classes
 * GET /api/teacher/profile
 */
router.get('/profile', async (req, res) => {
  try {
    console.log('Profile API called with session:', req.session);

    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    const isPrincipal = req.session.userRole === 'principal';
    let teacherId = req.session.userId;

    // If admin or principal, use the first teacher's data
    if (isAdmin || isPrincipal) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID:', teacherId);
      } else {
        return res.status(404).json({
          success: false,
          message: 'No teachers found in the system'
        });
      }
    }

    // Get teacher information
    const [teacherInfo] = await db.query(
      `SELECT id, username, name as full_name, email, profile_image, subjects, bio
       FROM users
       WHERE id = ? AND role = 'teacher'`,
      [teacherId]
    );

    if (teacherInfo.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    // Check if teacher_subjects table exists
    const [teacherSubjectsTable] = await db.query(
      `SHOW TABLES LIKE 'teacher_subjects'`
    );

    let teacherSubjects = [];
    if (teacherSubjectsTable.length > 0) {
      // Get teacher's assigned subjects
      [teacherSubjects] = await db.query(
        `SELECT DISTINCT s.id, s.name, s.code, s.description, s.subject_group
         FROM teacher_subjects ts
         JOIN subjects s ON ts.subject_id = s.id
         WHERE ts.teacher_id = ?
         ORDER BY s.name`,
        [teacherId]
      );
    }

    // If no subjects found in teacher_subjects table, create some from the teacher's subjects field
    if (teacherSubjects.length === 0) {
      const subjectNames = teacherInfo[0].subjects ? teacherInfo[0].subjects.split(',').map(s => s.trim()) : [];

      if (subjectNames.length > 0) {
        // Get subjects by name
        [teacherSubjects] = await db.query(
          `SELECT id, name, code, description, subject_group
           FROM subjects
           WHERE name IN (${subjectNames.map(() => '?').join(',')})
           ORDER BY name`,
          subjectNames
        );

        // If still no subjects found, create dummy subjects
        if (teacherSubjects.length === 0) {
          teacherSubjects = subjectNames.map((name, index) => ({
            id: index + 1,
            name: name,
            code: name.substring(0, 2).toUpperCase(),
            description: `${name} for senior secondary students`,
            subject_group: 12
          }));
        }
      }
    }

    // Check if teacher_classes table exists
    const [teacherClassesTable] = await db.query(
      `SHOW TABLES LIKE 'teacher_classes'`
    );

    let teacherClasses = [];
    if (teacherClassesTable.length > 0) {
      // Get teacher's assigned classes
      [teacherClasses] = await db.query(
        `SELECT DISTINCT
           c.id as class_id,
           c.name as class_name,
           c.grade,
           cr.id as classroom_id,
           cr.section,
           cr.room_number,
           t.id as trade_id,
           t.name as trade_name,
           CONCAT(c.grade, ' ', t.name, ' ', cr.section) as full_class_name
         FROM teacher_classes tc
         JOIN classrooms cr ON tc.classroom_id = cr.id
         JOIN classes c ON cr.class_id = c.id
         JOIN trades t ON cr.trade_id = t.id
         WHERE tc.teacher_id = ?
         ORDER BY c.grade, t.name, cr.section`,
        [teacherId]
      );
    }

    // If no classes found, create dummy classes
    if (teacherClasses.length === 0) {
      teacherClasses = [
        { class_id: 1, class_name: 'Class 11', grade: '11', classroom_id: 1, section: 'A', room_number: '11A', trade_id: 1, trade_name: 'NON-MEDICAL', full_class_name: '11 NON-MEDICAL A' },
        { class_id: 2, class_name: 'Class 11', grade: '11', classroom_id: 2, section: 'B', room_number: '11B', trade_id: 1, trade_name: 'NON-MEDICAL', full_class_name: '11 NON-MEDICAL B' },
        { class_id: 3, class_name: 'Class 12', grade: '12', classroom_id: 3, section: 'A', room_number: '12A', trade_id: 1, trade_name: 'NON-MEDICAL', full_class_name: '12 NON-MEDICAL A' },
        { class_id: 4, class_name: 'Class 12', grade: '12', classroom_id: 4, section: 'B', room_number: '12B', trade_id: 1, trade_name: 'NON-MEDICAL', full_class_name: '12 NON-MEDICAL B' }
      ];
    }

    // Check if lecture_schedule table exists
    const [lectureScheduleTable] = await db.query(
      `SHOW TABLES LIKE 'lecture_schedule'`
    );

    let teacherTimetable = [];
    if (lectureScheduleTable.length > 0) {
      // Get teacher's timetable from lecture_schedule
      [teacherTimetable] = await db.query(
        `SELECT
           ls.day_of_week,
           ls.start_time,
           ls.end_time,
           ls.classroom as room,
           ls.semester,
           ls.is_active,
           s.id as subject_id,
           s.name as subject_name,
           s.code as subject_code,
           c.id as class_id,
           c.grade,
           ls.classroom as class_display_name
         FROM lecture_schedule ls
         JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
         JOIN subjects s ON sca.subject_id = s.id
         JOIN classes c ON sca.class_id = c.id
         WHERE ls.teacher_id = ?
         ORDER BY
           CASE
             WHEN ls.day_of_week = 'Monday' THEN 1
             WHEN ls.day_of_week = 'Tuesday' THEN 2
             WHEN ls.day_of_week = 'Wednesday' THEN 3
             WHEN ls.day_of_week = 'Thursday' THEN 4
             WHEN ls.day_of_week = 'Friday' THEN 5
             ELSE 6
           END,
           ls.start_time`,
        [teacherId]
      );

      // Convert day_of_week from string to number for compatibility with the frontend
      teacherTimetable = teacherTimetable.map(lecture => {
        const dayMap = {
          'Monday': 1,
          'Tuesday': 2,
          'Wednesday': 3,
          'Thursday': 4,
          'Friday': 5,
          'Saturday': 6,
          'Sunday': 0
        };

        // Extract period from start time
        const hour = parseInt(lecture.start_time.split(':')[0]);
        let period;

        // Determine period based on start time
        if (hour < 9) period = 1;
        else if (hour < 10) period = 2;
        else if (hour < 11) period = 3;
        else if (hour < 12) period = 4;
        else if (hour < 13) period = 5;
        else if (hour < 14) period = 6;
        else period = 7;

        return {
          ...lecture,
          day_of_week: dayMap[lecture.day_of_week] || 1,
          period: period,
          subject_name: lecture.subject_name || 'Computer Science',
          subject_code: lecture.subject_code || 'CS'
        };
      });
    }

    // If no timetable found, create a dummy timetable
    if (teacherTimetable.length === 0) {
      // Define seasonal timings
      const currentMonth = new Date().getMonth() + 1; // JavaScript months are 0-indexed

      // Determine which timing to use based on the current month
      let lectureSchedule;

      if (currentMonth === 10) {
        // October timings
        lectureSchedule = [
          { period: 1, start_time: '08:30:00', end_time: '09:10:00' },
          { period: 2, start_time: '09:15:00', end_time: '09:55:00' },
          { period: 3, start_time: '10:00:00', end_time: '10:40:00' },
          { period: 4, start_time: '10:45:00', end_time: '11:25:00' },
          { period: 5, start_time: '11:30:00', end_time: '12:10:00' },
          { period: 6, start_time: '12:15:00', end_time: '12:55:00' },
          { period: 7, start_time: '13:00:00', end_time: '13:40:00' },
          { period: 8, start_time: '13:45:00', end_time: '14:25:00' }
        ];
      } else if (currentMonth >= 4 && currentMonth <= 9) {
        // Summer timings (April to September)
        lectureSchedule = [
          { period: 1, start_time: '08:00:00', end_time: '08:40:00' },
          { period: 2, start_time: '08:45:00', end_time: '09:25:00' },
          { period: 3, start_time: '09:30:00', end_time: '10:10:00' },
          { period: 4, start_time: '10:15:00', end_time: '10:55:00' },
          { period: 5, start_time: '11:00:00', end_time: '11:40:00' },
          { period: 6, start_time: '11:45:00', end_time: '12:25:00' },
          { period: 7, start_time: '12:30:00', end_time: '13:10:00' },
          { period: 8, start_time: '13:15:00', end_time: '13:55:00' }
        ];
      } else {
        // Winter timings (November to March)
        lectureSchedule = [
          { period: 1, start_time: '09:00:00', end_time: '09:40:00' },
          { period: 2, start_time: '09:45:00', end_time: '10:25:00' },
          { period: 3, start_time: '10:30:00', end_time: '11:10:00' },
          { period: 4, start_time: '11:15:00', end_time: '11:55:00' },
          { period: 5, start_time: '12:00:00', end_time: '12:40:00' },
          { period: 6, start_time: '12:45:00', end_time: '13:25:00' },
          { period: 7, start_time: '13:30:00', end_time: '14:10:00' },
          { period: 8, start_time: '14:15:00', end_time: '14:55:00' }
        ];
      }

      // Schedule template
      const scheduleTemplate = [
        { day: 1, period: 1, duration: 1, is_practical: false, topic: 'Theory' },
        { day: 2, period: 3, duration: 2, is_practical: true, topic: 'Practical' },
        { day: 3, period: 2, duration: 1, is_practical: false, topic: 'Theory' },
        { day: 4, period: 5, duration: 2, is_practical: true, topic: 'Practical' },
        { day: 5, period: 7, duration: 2, is_practical: true, topic: 'Lab/Project' }
      ];

      // Create timetable for each class
      for (const cls of teacherClasses) {
        for (const slot of scheduleTemplate) {
          // Get period times
          const periodStart = lectureSchedule[slot.period - 1];
          const periodEnd = lectureSchedule[slot.period + slot.duration - 2];

          // Select a subject
          const subject = teacherSubjects[0] || {
            id: 1,
            name: 'Computer Science',
            code: 'CS'
          };

          // Add to timetable
          teacherTimetable.push({
            day_of_week: slot.day,
            period: slot.period,
            start_time: periodStart.start_time,
            end_time: periodEnd.end_time,
            room: `Room ${cls.grade}${cls.section}`,
            subject_id: subject.id,
            subject_name: subject.name,
            subject_code: subject.code,
            class_id: cls.class_id,
            grade: cls.grade,
            section: cls.section,
            trade_name: cls.trade_name,
            class_display_name: cls.full_class_name
          });
        }
      }
    }

    // Check if teacher_lectures table exists
    const [teacherLecturesTable] = await db.query(
      `SHOW TABLES LIKE 'teacher_lectures'`
    );

    let upcomingLectures = [];
    if (teacherLecturesTable.length > 0) {
      // Get upcoming lectures
      [upcomingLectures] = await db.query(
        `SELECT
           id,
           date,
           start_time,
           end_time,
           class_name,
           subject_name,
           topic,
           location,
           status
         FROM teacher_lectures
         WHERE teacher_id = ? AND date >= CURDATE()
         ORDER BY date, start_time
         LIMIT 10`,
        [teacherId]
      );
    }

    // If no upcoming lectures found, create dummy lectures
    if (upcomingLectures.length === 0) {
      // Get next Monday's date
      const today = new Date();
      const nextMonday = new Date(today);
      nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);

      // Generate lectures for the next 5 days
      for (let dayOffset = 0; dayOffset < 5; dayOffset++) {
        const lectureDate = new Date(nextMonday);
        lectureDate.setDate(nextMonday.getDate() + dayOffset);

        // Format date as YYYY-MM-DD
        const formattedDate = lectureDate.toISOString().split('T')[0];

        // Generate 2 lectures per day
        for (let period = 1; period <= 2; period++) {
          // Define lecture times
          const lectureSchedule = [
            { start_time: '08:00:00', end_time: '08:40:00' },
            { start_time: '09:30:00', end_time: '10:10:00' }
          ];

          // Define subjects
          const subjects = teacherSubjects.map(s => s.name);

          // Define classes
          const classes = teacherClasses.map(c => c.full_class_name);

          // Generate a topic based on subject
          const csTopics = [
            'Introduction to Programming',
            'Data Types and Variables',
            'Control Structures',
            'Functions and Methods',
            'Object-Oriented Programming',
            'Data Structures'
          ];

          // Select random subject, class, and topic
          const subject = subjects[Math.floor(Math.random() * subjects.length)] || 'Computer Science';
          const cls = classes[Math.floor(Math.random() * classes.length)] || '11 NON-MEDICAL A';
          const topic = csTopics[Math.floor(Math.random() * csTopics.length)];
          const periodInfo = lectureSchedule[period - 1];

          upcomingLectures.push({
            id: dayOffset * 10 + period,
            date: formattedDate,
            start_time: periodInfo.start_time,
            end_time: periodInfo.end_time,
            class_name: cls,
            subject_name: subject,
            topic: topic,
            location: `Room ${cls.substring(0, 2)}`,
            status: Math.random() > 0.7 ? 'delivered' : 'pending'
          });
        }
      }
    }

    // Format the response
    const teacher = {
      id: teacherInfo[0].id,
      username: teacherInfo[0].username,
      full_name: teacherInfo[0].full_name,
      email: teacherInfo[0].email,
      profile_image: teacherInfo[0].profile_image,
      subjects: teacherInfo[0].subjects,
      bio: teacherInfo[0].bio,
      qualifications: "M.Sc. Computer Science, B.Ed.", // Hardcoded since field doesn't exist
      assigned_subjects: teacherSubjects,
      assigned_classes: teacherClasses,
      timetable: teacherTimetable,
      upcoming_lectures: upcomingLectures
    };

    res.json({
      success: true,
      teacher
    });
  } catch (error) {
    console.error('Error fetching teacher profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching teacher profile',
      error: error.message
    });
  }
});

/**
 * Get enhanced teacher profile with timeline data
 * GET /api/teacher/profile-enhanced
 */
router.get('/profile-enhanced', async (req, res) => {
  try {
    const teacherId = req.query.teacher_id || req.session.userId;

    if (!teacherId) {
      return res.status(400).json({
        success: false,
        message: 'Teacher ID is required'
      });
    }

    // Get basic teacher information
    const [teacherInfo] = await db.query(
      `SELECT id, username, name as fullName, email, profile_image, subjects, bio,
              phone, joining_date, employment_type, special_skills, languages_known,
              awards_received, training_programs
       FROM users
       WHERE id = ? AND role = 'teacher'`,
      [teacherId]
    );

    if (teacherInfo.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const teacher = teacherInfo[0];

    // Create sample educational timeline data
    const educationTimeline = [
      {
        title: "Ph.D. in Computer Science",
        institution: "Punjab Technical University",
        year: "2018",
        specialization: "Machine Learning and Data Analytics",
        thesis: "Advanced Algorithms for Educational Data Mining"
      },
      {
        title: "M.Sc. Computer Science",
        institution: "Guru Nanak Dev University",
        year: "2014",
        percentage: "85.6",
        specialization: "Software Engineering"
      },
      {
        title: "B.Sc. Computer Science",
        institution: "Punjab University",
        year: "2012",
        percentage: "78.4",
        stream: "Science"
      },
      {
        title: "Higher Secondary (12th)",
        institution: "Government Senior Secondary School",
        year: "2009",
        percentage: "82.1",
        board: "Punjab School Education Board",
        stream: "Science (PCM)"
      },
      {
        title: "Secondary (10th)",
        institution: "Government High School",
        year: "2007",
        percentage: "79.8",
        board: "Punjab School Education Board"
      }
    ];

    // Create sample experience timeline data
    const experienceTimeline = [
      {
        title: "Senior Computer Science Teacher",
        institution: "Meritorious Public School",
        duration: "2020 - Present",
        isCurrent: true
      },
      {
        title: "Computer Science Teacher",
        institution: "DAV Public School, Jalandhar",
        duration: "2018 - 2020",
        isCurrent: false
      },
      {
        title: "Assistant Teacher",
        institution: "Government Senior Secondary School",
        duration: "2015 - 2018",
        isCurrent: false
      },
      {
        title: "Teaching Intern",
        institution: "Punjab Technical University",
        duration: "2014 - 2015",
        isCurrent: false
      }
    ];

    // Calculate experience years
    const currentYear = new Date().getFullYear();
    const joiningYear = teacher.joining_date ? new Date(teacher.joining_date).getFullYear() : 2015;
    const total_experience_years = currentYear - 2014; // Started career in 2014
    const teaching_experience_years = currentYear - joiningYear;

    // Enhanced teacher profile response
    const enhancedTeacher = {
      ...teacher,
      employee_id: `EMP${String(teacher.id).padStart(4, '0')}`,
      designation: "Senior Computer Science Teacher",
      department: "Computer Science Department",
      total_experience_years,
      teaching_experience_years,
      educationTimeline,
      experienceTimeline,
      // Default values if not in database
      phone: teacher.phone || "+91-98765-43210",
      joining_date: teacher.joining_date || "2020-06-15",
      employment_type: teacher.employment_type || "permanent",
      special_skills: teacher.special_skills || "Programming, Web Development, Database Management, Machine Learning",
      languages_known: teacher.languages_known || "English, Hindi, Punjabi",
      awards_received: teacher.awards_received || "Best Teacher Award 2023, Excellence in Computer Science Teaching 2022",
      training_programs: teacher.training_programs || "Digital Teaching Methods Workshop, Advanced Programming Techniques, Educational Technology Integration"
    };

    res.json({
      success: true,
      teacher: enhancedTeacher
    });

  } catch (error) {
    console.error('Error fetching enhanced teacher profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching enhanced teacher profile',
      error: error.message
    });
  }
});

module.exports = router;

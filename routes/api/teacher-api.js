const express = require('express');
const router = express.Router();
const { checkAuthenticated } = require('../../middleware/auth');
const { isComputerTeacher, restrictComputerTeacher } = require('../../middleware/computer-teacher-check');
const teacherController = require('../../controllers/teacher-controller');
const teacherApiController = require('../../controllers/teacher-controller-api');
const teacherViewController = require('../../controllers/teacher-view-controller');

// Apply middleware to all routes
router.use(checkAuthenticated);

// Check if user is a teacher or admin
const checkTeacherOrAdmin = (req, res, next) => {
  if (req.session.userRole === 'teacher' || req.session.userRole === 'admin') {
    return next();
  }
  return res.status(403).json({
    success: false,
    message: 'Access denied. You must be a teacher or admin to access this resource.'
  });
};

router.use(checkTeacherOrAdmin);
router.use(isComputerTeacher);

// Get timetable data
router.get('/timetable', restrictComputerTeacher, teacherApiController.getTimetableData);

// Get teacher-specific timetable data
router.get('/teacher-timetable', restrictComputerTeacher, teacherApiController.getTeacherTimetableData);

// Get class-teacher-subject mind map data
router.get('/class-teacher-map', restrictComputerTeacher, teacherApiController.getClassTeacherMap);

// Get class-wise subject lecture data
router.get('/class-subject-data', restrictComputerTeacher, teacherApiController.getClassSubjectData);

// Get lecture by ID
router.get('/lectures/:id', restrictComputerTeacher, teacherApiController.getLectureById);

// Update lecture status
router.put('/lectures/:id/status', restrictComputerTeacher, teacherApiController.updateLectureStatus);

// Get practical by ID
router.get('/practicals/:id', restrictComputerTeacher, teacherApiController.getPracticalById);

// Update practical status
router.put('/practicals/:id/status', restrictComputerTeacher, teacherApiController.updatePracticalStatus);

// Add lecture
router.post('/lectures', restrictComputerTeacher, teacherController.addLecture);

// Add practical
router.post('/practicals', restrictComputerTeacher, teacherController.addPractical);

// Grade practical record
router.put('/practical-records/:id/grade', restrictComputerTeacher, teacherController.gradePracticalRecord);

// Update syllabus progress
router.post('/syllabus/progress', restrictComputerTeacher, teacherController.updateSyllabusProgress);

// Teacher view API routes
router.get('/subject-eligibility', restrictComputerTeacher, teacherViewController.getTeacherSubjectEligibilityAPI);
router.post('/subject-eligibility', restrictComputerTeacher, teacherViewController.addTeacherSubjectEligibilityAPI);
router.put('/subject-eligibility', restrictComputerTeacher, teacherViewController.updateTeacherSubjectEligibilityAPI);

router.get('/lecture-schedules', restrictComputerTeacher, teacherViewController.getLectureSchedulesAPI);
router.post('/lecture-schedules', restrictComputerTeacher, teacherViewController.addLectureScheduleAPI);
router.put('/lecture-schedules', restrictComputerTeacher, teacherViewController.updateLectureScheduleAPI);

router.get('/instruction-plans', restrictComputerTeacher, teacherViewController.getInstructionPlansAPI);
router.post('/instruction-plans', restrictComputerTeacher, teacherViewController.addInstructionPlanAPI);
router.put('/instruction-plans', restrictComputerTeacher, teacherViewController.updateInstructionPlanAPI);

router.get('/holiday-calendar', restrictComputerTeacher, teacherViewController.getHolidayCalendarAPI);
router.post('/holiday-calendar', restrictComputerTeacher, teacherViewController.addHolidayAPI);
router.put('/holiday-calendar', restrictComputerTeacher, teacherViewController.updateHolidayAPI);

module.exports = router;
